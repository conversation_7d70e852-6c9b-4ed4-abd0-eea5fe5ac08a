import React, { useState, useRef, useEffect, useCallback } from 'react';
import Message from './Message';
import MessageInput from './MessageInput';
import TypingIndicator from './TypingIndicator';
import PerformanceInfo from './PerformanceInfo';
import './Chat.css';

const Chat = () => {
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState(null);
  const [currentStreamingMessage, setCurrentStreamingMessage] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const messagesEndRef = useRef(null);
  const abortControllerRef = useRef(null);

  const API_KEY = 'test_api_key_1751884336144_vp9gospvg';
  const BASE_URL = 'http://localhost:3001';

  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, currentStreamingMessage, scrollToBottom]);

  const addMessage = useCallback((content, type, metadata = {}) => {
    setMessages(prev => [...prev, {
      id: Date.now() + Math.random(),
      content,
      type,
      timestamp: new Date(),
      ...metadata
    }]);
  }, []);

  const handleStreamingResponse = useCallback(async (response, startTime) => {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    let fullResponse = '';
    let firstChunkTime = null;
    let timingData = null;

    setIsStreaming(true);
    setCurrentStreamingMessage('');

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));

              if (data.type === 'content') {
                if (!firstChunkTime) {
                  firstChunkTime = Date.now();
                }
                
                fullResponse += data.content;
                setCurrentStreamingMessage(fullResponse);

              } else if (data.type === 'session') {
                setSessionId(data.sessionId);

              } else if (data.type === 'done') {
                const streamEndTime = Date.now();
                timingData = data.timing;
                
                // Finalize the message
                addMessage(fullResponse, 'bot');
                
                // Add performance info
                if (timingData) {
                  const totalTime = streamEndTime - startTime;
                  addMessage('', 'performance', { 
                    timing: timingData, 
                    totalTime,
                    firstChunkTime: firstChunkTime - startTime
                  });
                }

              } else if (data.type === 'error') {
                addMessage(`Error: ${data.message}`, 'error');
              }
            } catch (e) {
              console.warn('Failed to parse SSE data:', line);
            }
          }
        }
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        addMessage(`Stream error: ${error.message}`, 'error');
      }
    } finally {
      setIsStreaming(false);
      setCurrentStreamingMessage('');
    }
  }, [addMessage]);

  const sendMessage = useCallback(async (message) => {
    if (!message.trim() || isLoading) return;

    // Add user message
    addMessage(message, 'user');
    setIsLoading(true);

    // Create abort controller for this request
    abortControllerRef.current = new AbortController();

    try {
      const startTime = Date.now();
      
      const params = new URLSearchParams({
        apikey: API_KEY,
        query: message,
        stream: 'true',
        includeHistory: 'false'
      });

      if (sessionId) {
        params.append('sessionId', sessionId);
      }

      const url = `${BASE_URL}/api/v1/?${params.toString()}`;

      const response = await fetch(url, {
        headers: {
          'Origin': 'http://localhost:3001'
        },
        signal: abortControllerRef.current.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      await handleStreamingResponse(response, startTime);

    } catch (error) {
      if (error.name !== 'AbortError') {
        addMessage(`Error: ${error.message}`, 'error');
        console.error('Chat error:', error);
      }
    } finally {
      setIsLoading(false);
      abortControllerRef.current = null;
    }
  }, [isLoading, sessionId, addMessage, handleStreamingResponse]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return (
    <div className="chat-container">
      <div className="chat-header">
        <h1>💬 ChatAI Streaming</h1>
        <p>Ask questions about your documents - Real-time streaming responses</p>
      </div>

      <div className="chat-messages">
        {messages.length === 0 && (
          <div className="welcome-message">
            👋 Hello! Ask me anything about your documents.<br />
            <small>Try: "What is the invoice amount?" or "When is the due date?"</small>
          </div>
        )}

        {messages.map((message) => (
          <Message key={message.id} message={message} />
        ))}

        {isStreaming && currentStreamingMessage && (
          <Message 
            message={{
              content: currentStreamingMessage,
              type: 'bot',
              isStreaming: true
            }}
          />
        )}

        {isLoading && !isStreaming && <TypingIndicator />}
        
        <div ref={messagesEndRef} />
      </div>

      <MessageInput 
        onSendMessage={sendMessage}
        disabled={isLoading}
        isLoading={isLoading}
      />
    </div>
  );
};

export default Chat;
