/* Chat Container */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 900px;
  margin: 0 auto;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  overflow: hidden;
}

/* Chat Header */
.chat-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px;
  text-align: center;
  border-radius: 16px 16px 0 0;
}

.chat-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.chat-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
  font-weight: 400;
}

/* Messages Container */
.chat-messages {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: #f8fafc;
  scroll-behavior: smooth;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Welcome Message */
.welcome-message {
  text-align: center;
  color: #64748b;
  padding: 48px 24px;
  font-size: 18px;
  line-height: 1.6;
}

.welcome-message small {
  display: block;
  margin-top: 12px;
  font-size: 14px;
  opacity: 0.8;
}

/* Messages */
.message {
  max-width: 80%;
  padding: 16px 20px;
  border-radius: 20px;
  word-wrap: break-word;
  line-height: 1.5;
  font-size: 16px;
  position: relative;
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-user {
  align-self: flex-end;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom-right-radius: 8px;
}

.message-bot {
  align-self: flex-start;
  background: white;
  color: #1a202c;
  border: 1px solid #e2e8f0;
  border-bottom-left-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.message-bot.streaming {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

.message-error {
  align-self: flex-start;
  background: #fed7d7;
  color: #c53030;
  border: 1px solid #feb2b2;
  border-bottom-left-radius: 8px;
}

.message-content {
  margin: 0;
}

.message-timestamp {
  font-size: 12px;
  opacity: 0.7;
  margin-top: 8px;
  text-align: right;
}

/* Streaming Cursor */
.streaming-cursor {
  display: inline-block;
  width: 2px;
  height: 1em;
  background: #667eea;
  margin-left: 2px;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Typing Indicator */
.typing {
  background: #f1f5f9 !important;
  color: #64748b !important;
  font-style: italic;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #64748b;
  animation: typingPulse 1.4s infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingPulse {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  30% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Performance Info */
.performance-info {
  align-self: flex-start;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  color: #0369a1;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 13px;
  margin-top: -8px;
  max-width: 80%;
}

.performance-metrics {
  margin-bottom: 8px;
}

.metric {
  color: #0369a1;
}

.cache-info {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.cache-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
}

.cache-hit {
  background: #dcfce7;
  color: #166534;
}

.cache-miss {
  background: #fef2f2;
  color: #dc2626;
}

/* Input Container */
.chat-input {
  padding: 24px;
  background: white;
  border-top: 1px solid #e2e8f0;
  border-radius: 0 0 16px 16px;
}

.input-container {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.input-field {
  flex: 1;
  padding: 16px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 24px;
  background: #f8fafc;
  color: #1a202c;
  font-size: 16px;
  font-family: inherit;
  resize: none;
  outline: none;
  transition: all 0.2s ease;
  min-height: 56px;
  max-height: 120px;
}

.input-field:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-field::placeholder {
  color: #64748b;
  opacity: 0.8;
}

.input-field:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.send-button {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.send-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.send-button:active {
  transform: translateY(0);
}

.send-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .chat-container {
    height: 100vh;
    border-radius: 0;
    margin: 0;
  }

  .chat-header {
    padding: 20px;
    border-radius: 0;
  }

  .chat-header h1 {
    font-size: 24px;
  }

  .chat-messages {
    padding: 16px;
  }

  .message {
    max-width: 90%;
    padding: 12px 16px;
    font-size: 15px;
  }

  .chat-input {
    padding: 16px;
    border-radius: 0;
  }

  .input-field {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}
